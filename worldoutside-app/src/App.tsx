import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import MainLayout from "./layout/MainLayout";
import Login from "./pages/auth/Login";
import Visited from "./pages/Visited";

/**
 * Hlavný komponent aplikácie WorldOutside
 *
 * Tento komponent definuje štruktúru celej aplikácie a nastavuje routing.
 * Obsahuje všetky hlavné stránky aplikácie zabalené v MainLayout komponente,
 * ktorý poskytuje jednotný dizajn a navigáciu.
 *
 * Dostupné stránky:
 * - "/" - <PERSON><PERSON><PERSON> stránka s mapou
 * - "/prihlasenie" - Prihlasovacia stránka
 * - "/navstivene" - Zoznam navštívených miest
 * - "/profil" - Profil používateľa
 * - "/dashboard" - Dashboard s prehľadom aktivít
 *
 * @returns JSX.Element Hlavný komponent aplik<PERSON>cie s routingom
 */
function App() {
  return (
    <BrowserRouter>
      {/* Hlavný layout poskytujúci jednotný dizajn pre všetky stránky */}
      <MainLayout>
        <Routes>
          {/* Hlavná stránka s mapou a miestami */}
          <Route path="/" element={<Index />} />

          {/* Prihlasovacia stránka */}
          <Route path="/prihlasenie" element={<Login />} />

          {/* Stránka s navštívenými miestami */}
          <Route path="/navstivene" element={<Visited />} />

          {/* Profilová stránka používateľa (lazy loading) */}
          <Route path="/profil" element={React.createElement(require("./pages/Profile").default)} />

          {/* Dashboard s prehľadom aktivít (lazy loading) */}
          <Route path="/dashboard" element={React.createElement(require("./pages/Dashboard").default)} />

          {/* 404 stránka pre neexistujúce cesty */}
          <Route path="*" element={<div>404, Not Found!</div>} />
        </Routes>
      </MainLayout>
    </BrowserRouter>
  );
}

export default App;
