// Import potrebných knižníc a komponentov pre mapovú funkcionalitu
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, TileLayer, useMap } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { useEffect, useState } from "react";
import Markers from "./Markers";
import { getPlacesPlannedOptions } from "../../actions/getPlacesPlanned";
import { userMarkerIcon } from "./MarkerIcons";
import { Button, LoadingOverlay } from "@mantine/core";
import { useMutation, useQuery } from "@tanstack/react-query";
import { getPlacesOptions } from "../../actions/getPlaces";
import { mutationVisit } from "../../actions/mutationVisit";

import { submitCoordinates } from "../../actions/submitCoordinates";
import { panic } from "../../misc/notif";

/**
 * <PERSON>lavný mapový komponent aplikácie WorldOutside
 *
 * Tento komponent zobrazuje interaktívnu mapu s označenými vrcholmi/miestami,
 * ktoré môžu používatelia navštevovať. Poskytuje funkcionalitu pre:
 * - Zobrazenie aktuálnej polohy používateľa
 * - Označovanie miest ako navštívených
 * - Overovanie návštev pomocou GPS súradníc
 * - Plánovanie návštev vrcholov
 *
 * @returns JSX.Element Mapový komponent s interaktívnymi prvkami
 */
export default function Map() {

  // Stav pre pozíciu mapy (stred zobrazenia)
  const [mapPosition, setMapPosition] = useState<[number, number]>([0, 0]);

  // Stav pre aktuálnu GPS pozíciu používateľa
  const [userPosition, setUserPosition] = useState<[number, number]>([0, 0]);

  // ID miesta, ktoré sa práve overuje (pre zobrazenie loading stavu)
  // null znamená, že sa žiadne miesto neoveruje
  const [verifying, setVerifying] = useState<number | null>(null);

  // Query pre načítanie miest v okolí aktuálnej pozície mapy
  const { data, isPending, refetch } = useQuery(
    getPlacesOptions(mapPosition[0], mapPosition[1]),
  );

  // Query pre načítanie plánovaných vrcholov aktuálneho používateľa
  const { data: planned, refetch: refetchPlanned } = useQuery(getPlacesPlannedOptions());

  // Mutation pre označenie miesta ako navštíveného
  const postVisitedMutation = useMutation({
    mutationFn: (id: number) => mutationVisit(id),
  });

  /**
   * Effect hook pre získanie aktuálnej GPS pozície používateľa
   *
   * Pri načítaní komponentu sa pokúsi získať aktuálnu polohu používateľa
   * a nastaví ju ako východziu pozíciu pre mapu aj používateľský marker.
   */
  useEffect(() => {
    navigator.geolocation.getCurrentPosition((position) => {
      setUserPosition([position.coords.latitude, position.coords.longitude]);
      setMapPosition([position.coords.latitude, position.coords.longitude]);
    });
  }, []);

  /**
   * Komponent pre automatické precentrovanie mapy
   *
   * Tento vnútorný komponent sleduje zmeny v mapPosition a automaticky
   * presúva stred mapy na novú pozíciu pri zachovaní aktuálneho zoom levelu.
   *
   * @param mapPosition Nová pozícia, na ktorú sa má mapa precentrovať
   * @returns null (komponent nevracia žiadny JSX)
   */
  const RecenterAutomatically = ({
    mapPosition,
  }: {
    mapPosition: [number, number];
  }) => {
    const map = useMap();

    useEffect(() => {
      // Nastavenie nového centra mapy pri zachovaní aktuálneho zoom levelu
      map.setView(mapPosition, map.getZoom());
    }, [map, mapPosition]);

    return null;
  };

  /**
   * Komponent pre sledovanie pohybu mapy a aktualizáciu dát
   *
   * Tento komponent sleduje udalosť "dragend" (koniec ťahania mapy)
   * a aktualizuje mapPosition state, čo spôsobí nové načítanie miest
   * v novej oblasti mapy.
   *
   * @returns null (komponent nevracia žiadny JSX)
   */
  function MoveRefetch() {
    const map = useMap();

    useEffect(() => {
      // Nastavenie animácie pre plynulejší pohyb mapy
      map.options.easeLinearity = 0;

      // Pridanie event listenera pre koniec ťahania mapy
      map.on("dragend", () => {
        // Aktualizácia pozície mapy, čo spustí nové načítanie dát
        setMapPosition([map.getCenter().lat, map.getCenter().lng]);
      });

      // Cleanup funkcia pre odstránenie event listenera
      return () => {
        map.off("dragend");
      };
    }, [map]);

    return null;
  }


  /**
   * Handler pre označenie miesta ako navštíveného
   *
   * Táto funkcia sa volá, keď používateľ klikne na tlačidlo "Navštívené"
   * pri konkrétnom mieste. Odošle požiadavku na backend pre označenie
   * návštevy a po úspešnom dokončení aktualizuje dáta.
   *
   * @param id ID miesta, ktoré sa má označiť ako navštívené
   */
  function handleVisitedClick(id: number) {
    postVisitedMutation.mutate(id, {
      onSuccess: () => {
        // Po úspešnom označení znovu načítame dáta z backendu
        // pre získanie aktuálneho visit_type stavu
        refetch();
      },
      // onError je spracované v mutationVisit funkcii (zobrazí notifikáciu)
    });
  }

  /**
   * Handler pre overenie návštevy miesta pomocou GPS súradníc
   *
   * Táto funkcia sa volá, keď používateľ chce overiť svoju návštevu miesta.
   * Získa aktuálnu GPS pozíciu používateľa a odošle ju na backend pre overenie,
   * že sa používateľ skutočne nachádza v blízkosti daného miesta.
   *
   * @param id ID miesta, ktoré sa má overiť
   */
  async function handleVerifyClick(id: number) {
    // Nastavenie loading stavu pre dané miesto
    setVerifying(id);

    // Získanie aktuálnej GPS pozície používateľa
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          // Odoslanie GPS súradníc na backend pre overenie
          await submitCoordinates(
            id,
            position.coords.latitude,
            position.coords.longitude
          );
          // Po úspešnom overení znovu načítame dáta
          refetch();
        } catch (e: any) {
          // Spracovanie rôznych typov chýb
          if (e instanceof Error && e.message && e.message.includes("already visited")) {
            // Chyba: miesto už bolo overené
            panic({
              title: "Návšteva už bola overená",
              message: "Toto miesto ste už overili."
            });
          } else {
            // Všeobecná chyba pri overovaní
            panic({
              title: "Chyba pri overovaní",
              message: e?.message || "Nepodarilo sa overiť návštevu."
            });
          }
        } finally {
          // Vždy zrušíme loading stav
          setVerifying(null);
        }
      },
      () => {
        // Chyba pri získavaní GPS pozície
        panic({
          title: "Chyba geolokácie",
          message: "Nepodarilo sa získať polohu zariadenia."
        });
        setVerifying(null);
      }
    );
  }

  // Render hlavného mapového rozhrania
  return (
    <>
      {/* Loading overlay zobrazený počas načítavania dát */}
      <LoadingOverlay visible={isPending} zIndex={1000} />

      {/* Hlavný mapový kontajner s Leaflet mapou */}
      <MapContainer
        style={{ height: "100%", minHeight: 400, width: "100%" }}
        center={userPosition}  // Stred mapy nastavený na pozíciu používateľa
        zoom={14}              // Predvolený zoom level
        scrollWheelZoom={false} // Zakázanie zoom pomocou kolieska myši
      >
        {/* Vrstva s mapovými dlaždicami z OpenStreetMap */}
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Marker označujúci aktuálnu pozíciu používateľa */}
        <Marker icon={userMarkerIcon} position={userPosition}>
          <Popup>Tu si ty!</Popup>
        </Marker>

        {/* Renderovanie markerov pre všetky miesta/vrcholy */}
        {data && (
          <Markers
            data={data}                                              // Dáta o miestach
            handleVisitedClick={handleVisitedClick}                  // Handler pre označenie ako navštívené
            handleVerifyClick={handleVerifyClick}                    // Handler pre overenie návštevy
            verifying={verifying}                                    // ID miesta, ktoré sa práve overuje
            verifiedIds={[]}                                         // Prázdne pole (stav sa získava z backendu)
            softVisitedIds={[]}                                      // Prázdne pole (stav sa získava z backendu)
            plannedIds={planned ? planned.map((p: any) => p.id) : []} // ID plánovaných miest
            refetchPlanned={refetchPlanned}                          // Funkcia pre znovu načítanie plánovaných miest
            refetchAll={refetch}                                     // Funkcia pre znovu načítanie všetkých dát
          />
        )}

        {/* Komponent pre automatické precentrovanie mapy */}
        <RecenterAutomatically mapPosition={mapPosition} />

        {/* Komponent pre sledovanie pohybu mapy */}
        <MoveRefetch />

        {/* Tlačidlo pre návrat na pozíciu používateľa */}
        <Button
          onClick={() => setMapPosition(userPosition)}
          style={{ position: "absolute", bottom: 30, right: 10, zIndex: 1000 }}
        >
          My position
        </Button>
      </MapContainer>
    </>
  );
}
